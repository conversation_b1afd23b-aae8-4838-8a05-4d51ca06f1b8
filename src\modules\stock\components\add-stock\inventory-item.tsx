import { DatePickerInput } from "@/shared/components/custom/calendar-input";
import { FormField } from "@/shared/components/ui/form";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { BarcodeInput } from "@/shared/components/utils/barcode-input";
import { PriceInput } from "@/shared/components/form/price-input";
import { Box, HelpCircle, Package2, Trash2, Calculator, DollarSign, TrendingUp, Calendar, Barcode, Hash, FileText, AlertCircle } from "lucide-react";
import React, { useState } from "react";
import { Controller, UseFormReturn, useWatch, type FieldError } from "react-hook-form";
import { useCalculatedQuantity } from "../../hooks/quantity/calculate.hook";
import { ICreateStock } from "../../validators/create-stock.validator";
import { PricingCalculator } from "./pricing-calculator";

interface InventoryItemProps {
	index: number;
	methodsForm: UseFormReturn<ICreateStock>;
	removeItem: () => void;
	isExistingIdProduct: boolean;
	isExistingIdPackage: boolean;
}

export const InventoryItem: React.FC<InventoryItemProps> = ({
	index,
	methodsForm,
	removeItem,
	isExistingIdProduct = false,
	isExistingIdPackage = false,
}) => {
	const readonlyClass = "bg-gray-100 cursor-not-allowed text-gray-500";
	const [showPricingCalculator, setShowPricingCalculator] = useState(false);

	const quantityPerPackageValue = useWatch({
		control: methodsForm.control,
		name: `inventories.${index}.stockMovement.product.package.quantityPerPackage`,
	});
	const packageQuantity = useWatch({
		control: methodsForm.control,
		name: `inventories.${index}.stockMovement.packageQuantity`,
		defaultValue: undefined,
	});

	const fieldName = `inventories.${index}.stockMovement.quantity` as const;
	const { calculatedQuantity, handleQuantityChange } = useCalculatedQuantity({
		quantityPerPackageValue,
		packageQuantity,
		setValue: methodsForm.setValue,
		fieldName,
	});

	const getError = (path: string): FieldError | undefined => {
		const error = path.split(".").reduce(
			(acc: unknown, key: string) => {
				if (acc && typeof acc === "object" && key in acc) {
					return (acc as Record<string, unknown>)[key];
				}
				return undefined;
			},
			methodsForm.formState.errors as Record<string, unknown> | undefined
		);
		return error as FieldError | undefined;
	};

	const RequiredLabel = ({ children }: { children: React.ReactNode }) => (
		<span className="flex items-center">
			{children}
			<span className="text-red-500 ml-1">*</span>
		</span>
	);

	return (
		<div className="relative z-10 border border-gray-200 rounded-xl bg-gradient-to-br from-white to-gray-50/50 p-5 mb-4 transition-all hover:shadow-md hover:border-gray-300 group">
			<div className="flex items-center justify-between mb-4">
				<div className="flex items-center gap-3">
					<div className="p-2 bg-mainColor/10 rounded-lg group-hover:bg-mainColor/20 transition-colors">
						<Box size={18} className="text-mainColor" />
					</div>
					<div>
						<h4 className="text-sm font-semibold text-gray-700">Item {index + 1}</h4>
						<p className="text-xs text-gray-500">Configure os detalhes do produto</p>
					</div>
				</div>
				<button
					type="button"
					onClick={removeItem}
					className="text-red-500 hover:text-red-700 p-2 rounded-full hover:bg-red-50 transition-all hover:scale-105"
					title="Remover este item"
				>
					<Trash2 size={18} />
				</button>
			</div>
			<div className="bg-blue-50/50 rounded-lg p-4 mb-4 border border-blue-100">
				<div className="flex items-center gap-2 mb-3">
					<div className="p-1.5 bg-blue-500/10 rounded-lg">
						<Package2 size={16} className="text-blue-600" />
					</div>
					<span className="text-sm font-semibold text-blue-700">Detalhes do Produto</span>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div className="space-y-1">
						<Label className="text-xs flex items-center gap-1.5" htmlFor={`inventories.${index}.stockMovement.product.name`}>
							<FileText size={12} className="text-gray-500" />
							<RequiredLabel>Nome do Produto</RequiredLabel>
						</Label>
						<Input
							type="text"
							placeholder="Ex: Sabão em pó"
							readOnly={isExistingIdProduct}
							className={`text-sm transition-all ${isExistingIdProduct ? readonlyClass : "focus:ring-2 focus:ring-blue-500/20"} ${
								getError(`inventories.${index}.stockMovement.product.name`) ? "border-red-500 bg-red-50/50" : "border-gray-200"
							}`}
							{...methodsForm.register(`inventories.${index}.stockMovement.product.name`, {
								required: "Nome do produto é obrigatório",
							})}
						/>
						{getError(`inventories.${index}.stockMovement.product.name`) && (
							<span className="text-red-600 text-xs flex items-center gap-1">
								<AlertCircle size={12} />
								{getError(`inventories.${index}.stockMovement.product.name`)?.message}
							</span>
						)}
					</div>

					<div className="space-y-1">
						<Label className="text-xs flex items-center gap-1.5">
							<Barcode size={12} className="text-gray-500" />
							<RequiredLabel>
								Código de Barras
								<div className="relative group inline-block">
									<HelpCircle className="ml-1 w-3 h-3 text-gray-400 hover:text-gray-600 transition-colors" />
									<span className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 hidden w-max rounded-lg bg-gray-800 p-2 text-xs text-white group-hover:block shadow-lg z-10">
										Código de barras do produto
									</span>
								</div>
							</RequiredLabel>
						</Label>
						<Controller
							name={`inventories.${index}.stockMovement.product.barcode`}
							control={methodsForm.control}
							rules={{
								required: "Código de barras é obrigatório",
								pattern: {
									value: /^[0-9]*$/,
									message: "Apenas números são permitidos",
								},
							}}
							render={({ field }) => (
								<BarcodeInput
									value={field.value || ""}
									onChange={field.onChange}
									readOnly={isExistingIdProduct}
									error={getError(`inventories.${index}.stockMovement.product.barcode`)?.message}
								/>
							)}
						/>
						{getError(`inventories.${index}.stockMovement.product.barcode`) && (
							<span className="text-red-600 text-xs flex items-center gap-1">
								<AlertCircle size={12} />
								{getError(`inventories.${index}.stockMovement.product.barcode`)?.message}
							</span>
						)}
					</div>
				</div>
			</div>
			{/* Seção de Preços com Calculadora */}
			<div className="bg-green-50/50 rounded-lg p-4 mb-4 border border-green-100">
				<div className="flex items-center justify-between mb-3">
					<div className="flex items-center gap-2">
						<div className="p-1.5 bg-green-500/10 rounded-lg">
							<DollarSign size={16} className="text-green-600" />
						</div>
						<span className="text-sm font-semibold text-green-700">Preços</span>
					</div>
					{!isExistingIdProduct && (
						<button
							type="button"
							onClick={() => setShowPricingCalculator(!showPricingCalculator)}
							className={`p-2 rounded-lg transition-all hover:scale-105 ${
								showPricingCalculator
									? "bg-mainColor text-white shadow-md"
									: "bg-white text-mainColor border border-mainColor/20 hover:bg-mainColor/10"
							}`}
							title="Abrir calculadora de preços"
						>
							<Calculator size={16} />
						</button>
					)}
				</div>

				<div className="relative">
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div className="space-y-1">
							<PriceInput
								name={`inventories.${index}.stockMovement.product.costPrice`}
								control={methodsForm.control}
								label="Preço de Custo"
								required
								readOnly={isExistingIdProduct}
								className={isExistingIdProduct ? readonlyClass : ""}
							/>
						</div>
						<div className="space-y-1">
							<PriceInput
								name={`inventories.${index}.stockMovement.product.price`}
								control={methodsForm.control}
								label="Preço de Venda"
								required
								readOnly={isExistingIdProduct}
								className={isExistingIdProduct ? readonlyClass : ""}
							/>
						</div>
					</div>

					{/* Calculadora de Preços */}
					<PricingCalculator
						index={index}
						methodsForm={methodsForm}
						isOpen={showPricingCalculator}
						onClose={() => setShowPricingCalculator(false)}
					/>
				</div>
			</div>

			{/* Seção de Outros Detalhes */}
			<div className="bg-orange-50/50 rounded-lg p-4 mb-4 border border-orange-100">
				<div className="flex items-center gap-2 mb-3">
					<div className="p-1.5 bg-orange-500/10 rounded-lg">
						<Hash size={16} className="text-orange-600" />
					</div>
					<span className="text-sm font-semibold text-orange-700">Outros Detalhes</span>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					<div className="space-y-1">
						<Label className="text-xs flex items-center gap-1.5">
							<TrendingUp size={12} className="text-gray-500" />
							<RequiredLabel>Quantidade</RequiredLabel>
						</Label>
						<Controller
							name={`inventories.${index}.stockMovement.quantity`}
							control={methodsForm.control}
							rules={{
								required: "Quantidade é obrigatória",
								min: {
									value: 1,
									message: "Quantidade deve ser maior que zero",
								},
							}}
							render={({ field: { ref } }) => (
								<Input
									type="number"
									inputMode="numeric"
									pattern="[0-9]*"
									placeholder="0"
									value={calculatedQuantity}
									onChange={handleQuantityChange}
									ref={ref}
									className={`text-sm no-spinner transition-all focus:ring-2 focus:ring-orange-500/20 ${
										getError(`inventories.${index}.stockMovement.quantity`) ? "border-red-500 bg-red-50/50" : "border-gray-200"
									}`}
								/>
							)}
						/>
						{getError(`inventories.${index}.stockMovement.quantity`) && (
							<span className="text-red-600 text-xs flex items-center gap-1">
								<AlertCircle size={12} />
								{getError(`inventories.${index}.stockMovement.quantity`)?.message}
							</span>
						)}
					</div>

					<div className="space-y-1">
						<Label className="text-xs flex items-center gap-1.5">
							<Hash size={12} className="text-gray-500" />
							<RequiredLabel>Código do Produto</RequiredLabel>
						</Label>
						<Input
							type="text"
							placeholder="Ex: PROD-0001"
							readOnly={isExistingIdProduct}
							className={`text-sm transition-all ${isExistingIdProduct ? readonlyClass : "focus:ring-2 focus:ring-orange-500/20"} ${
								getError(`inventories.${index}.stockMovement.product.code`) ? "border-red-500 bg-red-50/50" : "border-gray-200"
							}`}
							{...methodsForm.register(`inventories.${index}.stockMovement.product.code`, {
								required: "Código do produto é obrigatório",
							})}
						/>
						{getError(`inventories.${index}.stockMovement.product.code`) && (
							<span className="text-red-600 text-xs flex items-center gap-1">
								<AlertCircle size={12} />
								{getError(`inventories.${index}.stockMovement.product.code`)?.message}
							</span>
						)}
					</div>

					<div className="space-y-1">
						<Label className="text-xs flex items-center gap-1.5">
							<FileText size={12} className="text-gray-500" />
							<RequiredLabel>NCM</RequiredLabel>
						</Label>
						<Input
							type="text"
							placeholder="Ex: 1234.56.78"
							readOnly={isExistingIdProduct}
							className={`text-sm transition-all ${isExistingIdProduct ? readonlyClass : "focus:ring-2 focus:ring-orange-500/20"} ${
								getError(`inventories.${index}.stockMovement.product.ncm`) ? "border-red-500 bg-red-50/50" : "border-gray-200"
							}`}
							{...methodsForm.register(`inventories.${index}.stockMovement.product.ncm`, {
								required: "NCM é obrigatório",
								pattern: {
									value: /^\d{4}\.\d{2}\.\d{2}$/,
									message: "Formato inválido. Use o formato: 1234.56.78",
								},
							})}
						/>
						{getError(`inventories.${index}.stockMovement.product.ncm`) && (
							<span className="text-red-600 text-xs flex items-center gap-1">
								<AlertCircle size={12} />
								{getError(`inventories.${index}.stockMovement.product.ncm`)?.message}
							</span>
						)}
					</div>
				</div>

				<div className="mt-4">
					<div className="flex items-center gap-1.5 mb-2">
						<Calendar size={12} className="text-gray-500" />
						<span className="text-xs font-medium text-gray-600">Data de Validade</span>
					</div>
					<FormField
						name={`inventories.${index}.expirationDate`}
						control={methodsForm.control}
						rules={{
							required: "Data de validade é obrigatória",
						}}
						render={({ field }) => (
							<DatePickerInput
								className="w-full md:w-1/3"
								field={field}
								inputDateClassName={`text-sm transition-all focus:ring-2 focus:ring-orange-500/20 ${
									getError(`inventories.${index}.expirationDate`) ? "border-red-500 bg-red-50/50" : "border-gray-200"
								}`}
								label=""
								labelClassName="text-xs"
							/>
						)}
					/>
					{getError(`inventories.${index}.expirationDate`) && (
						<span className="text-red-600 text-xs flex items-center gap-1 mt-1">
							<AlertCircle size={12} />
							{getError(`inventories.${index}.expirationDate`)?.message}
						</span>
					)}
				</div>
			</div>
			{/* Seção da Caixa */}
			<div className="bg-purple-50/50 rounded-lg p-4 border border-purple-100">
				<div className="flex items-center gap-2 mb-3">
					<div className="p-1.5 bg-purple-500/10 rounded-lg">
						<Package2 size={16} className="text-purple-600" />
					</div>
					<span className="text-sm font-semibold text-purple-700">Detalhes da Caixa</span>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
					<div className="space-y-1">
						<Label className="text-xs flex items-center gap-1.5">
							<FileText size={12} className="text-gray-500" />
							<RequiredLabel>Nome da Caixa</RequiredLabel>
						</Label>
						<Controller
							name={`inventories.${index}.stockMovement.product.package.name`}
							control={methodsForm.control}
							rules={{
								required: !isExistingIdPackage ? "Nome da caixa é obrigatório" : false,
							}}
							render={({ field }) => (
								<Input
									type="text"
									placeholder="Ex: Caixa Sabão 12 un."
									readOnly={isExistingIdPackage}
									className={`text-sm transition-all ${isExistingIdPackage ? readonlyClass : "focus:ring-2 focus:ring-purple-500/20"} ${
										methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package?.name
											? "border-red-500 bg-red-50/50"
											: "border-gray-200"
									}`}
									{...field}
								/>
							)}
						/>
						{methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package?.name && (
							<span className="text-red-600 text-xs flex items-center gap-1">
								<AlertCircle size={12} />
								{methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package?.name?.message}
							</span>
						)}
					</div>

					<div className="space-y-1">
						<Label className="text-xs flex items-center gap-1.5">
							<Barcode size={12} className="text-gray-500" />
							<RequiredLabel>
								Código de Barras da Caixa
								<div className="relative group inline-block">
									<HelpCircle className="ml-1 w-3 h-3 text-gray-400 hover:text-gray-600 transition-colors" />
									<span className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 hidden w-max rounded-lg bg-gray-800 p-2 text-xs text-white group-hover:block shadow-lg z-10">
										Código de barras da embalagem maior (caixa)
									</span>
								</div>
							</RequiredLabel>
						</Label>
						<Controller
							name={`inventories.${index}.stockMovement.product.package.barcode`}
							control={methodsForm.control}
							rules={{
								required: !isExistingIdPackage ? "Código de barras da caixa é obrigatório" : false,
								pattern: {
									value: /^[0-9]*$/,
									message: "Apenas números são permitidos",
								},
							}}
							render={({ field }) => (
								<Input
									type="string"
									placeholder="Ex: 123456789012345"
									readOnly={isExistingIdPackage}
									className={`text-sm transition-all ${isExistingIdPackage ? readonlyClass : "focus:ring-2 focus:ring-purple-500/20"} ${
										methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package?.barcode
											? "border-red-500 bg-red-50/50"
											: "border-gray-200"
									}`}
									{...field}
								/>
							)}
						/>
						{methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package?.barcode && (
							<span className="text-red-600 text-xs flex items-center gap-1">
								<AlertCircle size={12} />
								{methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package?.barcode?.message}
							</span>
						)}
					</div>

					<div className="space-y-1">
						<Label className="text-xs flex items-center gap-1.5">
							<Hash size={12} className="text-gray-500" />
							<RequiredLabel>Código da Caixa</RequiredLabel>
						</Label>
						<Controller
							name={`inventories.${index}.stockMovement.product.package.code`}
							control={methodsForm.control}
							rules={{
								required: !isExistingIdPackage ? "Código da caixa é obrigatório" : false,
							}}
							render={({ field }) => (
								<Input
									type="text"
									placeholder="Ex: CX-0001"
									readOnly={isExistingIdPackage}
									className={`text-sm transition-all ${isExistingIdPackage ? readonlyClass : "focus:ring-2 focus:ring-purple-500/20"} ${
										methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package?.code
											? "border-red-500 bg-red-50/50"
											: "border-gray-200"
									}`}
									{...field}
								/>
							)}
						/>
						{methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package?.code && (
							<span className="text-red-600 text-xs flex items-center gap-1">
								<AlertCircle size={12} />
								{methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package?.code?.message}
							</span>
						)}
					</div>

					<div className="space-y-1">
						<Label className="text-xs flex items-center gap-1.5">
							<TrendingUp size={12} className="text-gray-500" />
							<RequiredLabel>Itens por Caixa</RequiredLabel>
						</Label>
						<Controller
							name={`inventories.${index}.stockMovement.product.package.quantityPerPackage`}
							control={methodsForm.control}
							rules={{
								required: !isExistingIdPackage ? "Quantidade por caixa é obrigatória" : false,
								min: {
									value: 1,
									message: "Quantidade deve ser maior que zero",
								},
							}}
							render={({ field }) => (
								<Input
									type="number"
									inputMode="numeric"
									pattern="[0-9]*"
									placeholder="Ex: 12"
									className={`text-sm no-spinner transition-all focus:ring-2 focus:ring-purple-500/20 ${
										methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package?.quantityPerPackage
											? "border-red-500 bg-red-50/50"
											: "border-gray-200"
									}`}
									{...field}
								/>
							)}
						/>
						{methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package?.quantityPerPackage && (
							<span className="text-red-600 text-xs flex items-center gap-1">
								<AlertCircle size={12} />
								{methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package?.quantityPerPackage?.message}
							</span>
						)}
					</div>
				</div>

				{Object.keys(methodsForm.formState.errors?.inventories?.[index]?.stockMovement?.product?.package || {}).length > 0 && (
					<div className="flex items-center justify-center gap-2 mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
						<AlertCircle size={16} className="text-red-500" />
						<span className="text-red-600 text-sm font-medium">Preencha todos os campos obrigatórios da caixa</span>
					</div>
				)}
			</div>
		</div>
	);
};
